//board
let board;
const rowCount = 21;
const columnCount = 19;
const tileSize = 32;
const boardWidth = columnCount*tileSize;
const boardHeight = rowCount*tileSize;
let context;

let blueGhostImage;
let orangeGhostImage;
let pinkGhostImage;
let redGhostImage;
let scaredGhostImage;
let wallImage;

//X = wall, O = skip, D = dog, ' ' = food, W = power pellet
//Ghosts: b = blue, o = orange, p = pink, r = red
const tileMap = [
    "XXXXXXXXXXXXXXXXXXX",
    "XW       X       WX",
    "X XX XXX X XXX XX X",
    "X                 X",
    "X XX X XXXXX X XX X",
    "X    X       X    X",
    "XXXX XXXX XXXX XXXX",
    "OOOX X       X XOOO",
    "XXXX X XXrXX X XXXX",
    "O       bpo       O",
    "XXXX X XXXXX X XXXX",
    "OOOX X       X XOOO",
    "XXXX X XXXXX X XXXX",
    "X        X        X",
    "X XX XXX X XXX XX X",
    "XW X     D     X WX",
    "XX X X XXXXX X X XX",
    "X    X   X   X    X",
    "X XXXXXX X XXXXXX X",
    "X                 X",
    "XXXXXXXXXXXXXXXXXXX" 
];

const walls = new Set();
const foods = new Set();
const powerPellets = new Set();
const ghosts = new Set();
let dog;
let dogElement;

const directions = ['U', 'D', 'L', 'R']; //up down left right
let score = 0;
let lives = 3;
let gameOver = false;
let powerMode = false;
let powerModeTimer = 0;
const powerModeDuration = 300; // 15 seconds at 20 FPS (300 frames)

window.onload = function() {
    console.log("Dog Pac-Man starting...");
    
    board = document.getElementById("board");
    board.height = boardHeight;
    board.width = boardWidth;
    context = board.getContext("2d");
    
    console.log("Canvas initialized");
    
    loadImages();
    loadMap();
    createDogElement();
    
    console.log("Map loaded - walls:", walls.size, "foods:", foods.size, "powerPellets:", powerPellets.size, "ghosts:", ghosts.size);
    
    for (let ghost of ghosts.values()) {
        const newDirection = directions[Math.floor(Math.random()*4)];
        ghost.updateDirection(newDirection);
    }
    
    update();
    document.addEventListener("keyup", moveDog);
    
    console.log("Game loop started");
}

function createDogElement() {
    // Create dog emoji element
    dogElement = document.createElement('div');
    dogElement.className = 'dog-emoji';
    dogElement.textContent = '🐕';
    
    // Position the dog
    dogElement.style.left = (dog.x + 2) + 'px';
    dogElement.style.top = (dog.y + 2) + 'px';
    
    // Add to the page
    document.body.appendChild(dogElement);
}

function updateDogPosition() {
    if (dogElement) {
        dogElement.style.left = (dog.x + 2) + 'px';
        dogElement.style.top = (dog.y + 2) + 'px';
        
        // Update dog direction
        if (dog.direction === 'L') {
            dogElement.style.transform = 'scaleX(-1)';
        } else {
            dogElement.style.transform = 'scaleX(1)';
        }
        
        // Add happy animation during power mode
        if (powerMode) {
            dogElement.classList.add('dog-happy');
        } else {
            dogElement.classList.remove('dog-happy');
        }
    }
}

function loadImages() {
    wallImage = new Image();
    wallImage.src = "./wall.png";

    blueGhostImage = new Image();
    blueGhostImage.src = "./blueGhost.png";
    orangeGhostImage = new Image()
    orangeGhostImage.src = "./orangeGhost.png"
    pinkGhostImage = new Image()
    pinkGhostImage.src = "./pinkGhost.png";
    redGhostImage = new Image()
    redGhostImage.src = "./redGhost.png";
    scaredGhostImage = new Image();
    scaredGhostImage.src = "./scaredGhost.png";
}

function loadMap() {
    walls.clear();
    foods.clear();
    powerPellets.clear();
    ghosts.clear();

    for (let r = 0; r < rowCount; r++) {
        for (let c = 0; c < columnCount; c++) {
            const row = tileMap[r];
            const tileMapChar = row[c];

            const x = c*tileSize;
            const y = r*tileSize;

            if (tileMapChar == 'X') { //block wall
                const wall = new Block(wallImage, x, y, tileSize, tileSize);
                walls.add(wall);  
            }
            else if (tileMapChar == 'b') { //blue ghost
                const ghost = new Block(blueGhostImage, x, y, tileSize, tileSize);
                ghost.originalImage = blueGhostImage;
                ghost.isScared = false;
                ghosts.add(ghost);
            }
            else if (tileMapChar == 'o') { //orange ghost
                const ghost = new Block(orangeGhostImage, x, y, tileSize, tileSize);
                ghost.originalImage = orangeGhostImage;
                ghost.isScared = false;
                ghosts.add(ghost);
            }
            else if (tileMapChar == 'p') { //pink ghost
                const ghost = new Block(pinkGhostImage, x, y, tileSize, tileSize);
                ghost.originalImage = pinkGhostImage;
                ghost.isScared = false;
                ghosts.add(ghost);
            }
            else if (tileMapChar == 'r') { //red ghost
                const ghost = new Block(redGhostImage, x, y, tileSize, tileSize);
                ghost.originalImage = redGhostImage;
                ghost.isScared = false;
                ghosts.add(ghost);
            }
            else if (tileMapChar == 'D') { //dog
                dog = new Block(null, x, y, tileSize, tileSize);
            }
            else if (tileMapChar == ' ') { //empty is food (treats)
                const food = new Block(null, x + 14, y + 14, 4, 4);
                foods.add(food);
            }
            else if (tileMapChar == 'W') { //power pellet (big treats)
                const powerPellet = new Block(null, x + 8, y + 8, 16, 16);
                powerPellets.add(powerPellet);
            }
        }
    }
}

function update() {
    if (gameOver) {
        return;
    }
    move();
    draw();
    updateDogPosition();
    setTimeout(update, 50); //1000/50 = 20 FPS
}

function draw() {
    context.clearRect(0, 0, board.width, board.height);
    
    // Draw ghosts
    for (let ghost of ghosts.values()) {
        if (ghost.image && ghost.image.complete) {
            context.drawImage(ghost.image, ghost.x, ghost.y, ghost.width, ghost.height);
        } else {
            context.fillStyle = ghost.isScared ? "blue" : "red";
            context.fillRect(ghost.x, ghost.y, ghost.width, ghost.height);
        }
    }
    
    // Draw walls
    for (let wall of walls.values()) {
        if (wall.image && wall.image.complete) {
            context.drawImage(wall.image, wall.x, wall.y, wall.width, wall.height);
        } else {
            context.fillStyle = "blue";
            context.fillRect(wall.x, wall.y, wall.width, wall.height);
        }
    }

    // Draw food dots (small treats)
    context.fillStyle = "#8B4513";
    for (let food of foods.values()) {
        context.beginPath();
        context.arc(food.x + food.width/2, food.y + food.height/2, food.width/2, 0, 2 * Math.PI);
        context.fill();
    }

    // Draw power pellets (big treats)
    context.fillStyle = "#D2691E";
    for (let powerPellet of powerPellets.values()) {
        context.beginPath();
        context.arc(powerPellet.x + powerPellet.width/2, powerPellet.y + powerPellet.height/2, powerPellet.width/2, 0, 2 * Math.PI);
        context.fill();
    }

    // Draw score
    context.fillStyle = "white";
    context.font="14px sans-serif";
    if (gameOver) {
        context.fillText("Game Over: " + String(score), tileSize/2, tileSize/2);
    }
    else {
        context.fillText("🐕 x" + String(lives) + " Score: " + String(score), tileSize/2, tileSize/2);
    }
}

function move() {
    dog.x += dog.velocityX;
    dog.y += dog.velocityY;

    // Screen wrapping for dog
    if (dog.x + dog.width < 0) {
        dog.x = boardWidth;
    }
    else if (dog.x > boardWidth) {
        dog.x = -dog.width;
    }

    // Check wall collisions
    for (let wall of walls.values()) {
        if (collision(dog, wall)) {
            dog.x -= dog.velocityX;
            dog.y -= dog.velocityY;
            break;
        }
    }

    // Update ghost scared state
    for (let ghost of ghosts.values()) {
        if (powerMode && !ghost.isScared) {
            ghost.isScared = true;
            ghost.image = scaredGhostImage;
        } else if (!powerMode && ghost.isScared) {
            ghost.isScared = false;
            ghost.image = ghost.originalImage;
        }
    }

    // Check ghost collisions
    let ghostEaten = null;
    for (let ghost of ghosts.values()) {
        if (collision(ghost, dog)) {
            if (powerMode && ghost.isScared) {
                ghostEaten = ghost;
                score += 200;
            } else {
                lives -= 1;
                if (lives == 0) {
                    gameOver = true;
                    return;
                }
                resetPositions();
                return;
            }
        }

        if (ghost.y == tileSize*9 && ghost.direction != 'U' && ghost.direction != 'D') {
            ghost.updateDirection('U');
        }

        ghost.x += ghost.velocityX;
        ghost.y += ghost.velocityY;

        // Screen wrapping for ghosts
        if (ghost.x + ghost.width < 0) {
            ghost.x = boardWidth;
        }
        else if (ghost.x > boardWidth) {
            ghost.x = -ghost.width;
        }

        for (let wall of walls.values()) {
            if (collision(ghost, wall)) {
                ghost.x -= ghost.velocityX;
                ghost.y -= ghost.velocityY;
                const newDirection = directions[Math.floor(Math.random()*4)];
                ghost.updateDirection(newDirection);
            }
        }
    }

    // Respawn eaten ghost
    if (ghostEaten) {
        ghostEaten.reset();
        ghostEaten.isScared = false;
        ghostEaten.image = ghostEaten.originalImage;
    }

    // Check food collision (treats)
    let foodEaten = null;
    for (let food of foods.values()) {
        if (collision(dog, food)) {
            foodEaten = food;
            score += 10;
            break;
        }
    }
    foods.delete(foodEaten);

    // Check power pellet collision (big treats)
    let powerPelletEaten = null;
    for (let powerPellet of powerPellets.values()) {
        if (collision(dog, powerPellet)) {
            powerPelletEaten = powerPellet;
            score += 50;
            powerMode = true;
            powerModeTimer = powerModeDuration;
            break;
        }
    }
    powerPellets.delete(powerPelletEaten);

    // Update power mode timer
    if (powerMode) {
        powerModeTimer--;
        if (powerModeTimer <= 0) {
            powerMode = false;
        }
    }

    // Next level
    if (foods.size == 0 && powerPellets.size == 0) {
        loadMap();
        resetPositions();
    }
}

function moveDog(e) {
    if (gameOver) {
        loadMap();
        resetPositions();
        lives = 3;
        score = 0;
        gameOver = false;
        powerMode = false;
        powerModeTimer = 0;
        update();
        return;
    }

    if (e.code == "ArrowUp" || e.code == "KeyW") {
        dog.updateDirection('U');
    }
    else if (e.code == "ArrowDown" || e.code == "KeyS") {
        dog.updateDirection('D');
    }
    else if (e.code == "ArrowLeft" || e.code == "KeyA") {
        dog.updateDirection('L');
    }
    else if (e.code == "ArrowRight" || e.code == "KeyD") {
        dog.updateDirection('R');
    }
}

function collision(a, b) {
    return a.x < b.x + b.width &&
           a.x + a.width > b.x &&
           a.y < b.y + b.height &&
           a.y + a.height > b.y;
}

function resetPositions() {
    dog.reset();
    dog.velocityX = 0;
    dog.velocityY = 0;
    powerMode = false;
    powerModeTimer = 0;

    // Recreate dog element
    if (dogElement) {
        dogElement.remove();
    }
    createDogElement();

    for (let ghost of ghosts.values()) {
        ghost.reset();
        ghost.isScared = false;
        ghost.image = ghost.originalImage;
        const newDirection = directions[Math.floor(Math.random()*4)];
        ghost.updateDirection(newDirection);
    }
}

class Block {
    constructor(image, x, y, width, height) {
        this.image = image;
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;

        this.startX = x;
        this.startY = y;

        this.direction = 'R';
        this.velocityX = 0;
        this.velocityY = 0;
    }

    updateDirection(direction) {
        const prevDirection = this.direction;
        this.direction = direction;
        this.updateVelocity();
        this.x += this.velocityX;
        this.y += this.velocityY;

        for (let wall of walls.values()) {
            if (collision(this, wall)) {
                this.x -= this.velocityX;
                this.y -= this.velocityY;
                this.direction = prevDirection;
                this.updateVelocity();
                return;
            }
        }
    }

    updateVelocity() {
        if (this.direction == 'U') {
            this.velocityX = 0;
            this.velocityY = -tileSize/4;
        }
        else if (this.direction == 'D') {
            this.velocityX = 0;
            this.velocityY = tileSize/4;
        }
        else if (this.direction == 'L') {
            this.velocityX = -tileSize/4;
            this.velocityY = 0;
        }
        else if (this.direction == 'R') {
            this.velocityX = tileSize/4;
            this.velocityY = 0;
        }
    }

    reset() {
        this.x = this.startX;
        this.y = this.startY;
    }
}
