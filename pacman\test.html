<!DOCTYPE html>
<html>
<head>
    <title>Image Test</title>
</head>
<body>
    <h1>Testing Pacman Images</h1>
    <div>
        <h2>Wall</h2>
        <img src="./wall.png" alt="Wall" style="border: 1px solid red;">
    </div>
    <div>
        <h2>Ghosts</h2>
        <img src="./blueGhost.png" alt="Blue Ghost" style="border: 1px solid red;">
        <img src="./orangeGhost.png" alt="Orange Ghost" style="border: 1px solid red;">
        <img src="./pinkGhost.png" alt="Pink Ghost" style="border: 1px solid red;">
        <img src="./redGhost.png" alt="Red Ghost" style="border: 1px solid red;">
        <img src="./scaredGhost.png" alt="Scared Ghost" style="border: 1px solid red;">
    </div>
    <div>
        <h2>Pacman</h2>
        <img src="./pacmanUp.png" alt="Pacman Up" style="border: 1px solid red;">
        <img src="./pacmanDown.png" alt="Pacman Down" style="border: 1px solid red;">
        <img src="./pacmanLeft.png" alt="Pacman Left" style="border: 1px solid red;">
        <img src="./pacmanRight.png" alt="Pacman Right" style="border: 1px solid red;">
    </div>
    
    <script>
        // Test if images load
        const images = document.querySelectorAll('img');
        images.forEach((img, index) => {
            img.onload = () => console.log(`Image ${index} loaded:`, img.src);
            img.onerror = () => console.error(`Image ${index} failed:`, img.src);
        });
    </script>
</body>
</html>
