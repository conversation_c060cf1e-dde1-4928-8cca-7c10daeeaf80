//board
let board;
const rowCount = 21;
const columnCount = 19;
const tileSize = 32;
const boardWidth = columnCount*tileSize;
const boardHeight = rowCount*tileSize;
let context;

let blueGhostImage;
let orangeGhostImage;
let pinkGhostImage;
let redGhostImage;
let scaredGhostImage;
let pacmanUpImage;
let pacmanDownImage;
let pacmanLeftImage;
let pacmanRightImage;
let wallImage;

//X = wall, O = skip, P = pac man, ' ' = food, W = power pellet
//Ghosts: b = blue, o = orange, p = pink, r = red
const tileMap = [
    "XXXXXXXXXXXXXXXXXXX",
    "XW       X       WX",
    "X XX XXX X XXX XX X",
    "X                 X",
    "X XX X XXXXX X XX X",
    "X    X       X    X",
    "XXXX XXXX XXXX XXXX",
    "OOOX X       X XOOO",
    "XXXX X XXrXX X XXXX",
    "O       bpo       O",
    "XXXX X XXXXX X XXXX",
    "OOOX X       X XOOO",
    "XXXX X XXXXX X XXXX",
    "X        X        X",
    "X XX XXX X XXX XX X",
    "XW X     P     X WX",
    "XX X X XXXXX X X XX",
    "X    X   X   X    X",
    "X XXXXXX X XXXXXX X",
    "X                 X",
    "XXXXXXXXXXXXXXXXXXX"
];

const walls = new Set();
const foods = new Set();
const powerPellets = new Set();
const ghosts = new Set();
let pacman;

const directions = ['U', 'D', 'L', 'R']; //up down left right
let score = 0;
let lives = 3;
let gameOver = false;
let powerMode = false;
let powerModeTimer = 0;
const powerModeDuration = 300; // 15 seconds at 20 FPS (300 frames)

window.onload = function() {
    console.log("Window loaded, initializing game...");
    board = document.getElementById("board");
    board.height = boardHeight;
    board.width = boardWidth;
    context = board.getContext("2d"); //used for drawing on the board

    // Test canvas rendering
    context.fillStyle = "red";
    context.fillRect(10, 10, 50, 50);
    console.log("Canvas test rectangle drawn");

    loadImages();

    // Wait for images to load before starting the game, with timeout fallback
    let imagesLoaded = 0;
    let imageErrors = 0;
    const totalImages = 10; // wall, 4 ghosts, scared ghost, 4 pacman directions

    function imageLoaded() {
        imagesLoaded++;
        console.log("Image loaded:", imagesLoaded, "/", totalImages);
        checkAllImagesProcessed();
    }

    function imageError(e) {
        imageErrors++;
        console.error("Failed to load image:", e.target.src);
        checkAllImagesProcessed();
    }

    function checkAllImagesProcessed() {
        if (imagesLoaded + imageErrors >= totalImages) {
            console.log("All images processed. Loaded:", imagesLoaded, "Errors:", imageErrors);
            startGame();
        }
    }

    // Fallback timeout - start game after 5 seconds regardless
    setTimeout(() => {
        if (imagesLoaded + imageErrors < totalImages) {
            console.log("Timeout reached, starting game anyway...");
            startGame();
        }
    }, 5000);

    // Add load event listeners to all images
    wallImage.onload = imageLoaded;
    wallImage.onerror = imageError;
    blueGhostImage.onload = imageLoaded;
    blueGhostImage.onerror = imageError;
    orangeGhostImage.onload = imageLoaded;
    orangeGhostImage.onerror = imageError;
    pinkGhostImage.onload = imageLoaded;
    pinkGhostImage.onerror = imageError;
    redGhostImage.onload = imageLoaded;
    redGhostImage.onerror = imageError;
    scaredGhostImage.onload = imageLoaded;
    scaredGhostImage.onerror = imageError;
    pacmanUpImage.onload = imageLoaded;
    pacmanUpImage.onerror = imageError;
    pacmanDownImage.onload = imageLoaded;
    pacmanDownImage.onerror = imageError;
    pacmanLeftImage.onload = imageLoaded;
    pacmanLeftImage.onerror = imageError;
    pacmanRightImage.onload = imageLoaded;
    pacmanRightImage.onerror = imageError;
}

function startGame() {
    console.log("Starting game...");
    loadMap();
    console.log("Map loaded - walls:", walls.size, "foods:", foods.size, "powerPellets:", powerPellets.size, "ghosts:", ghosts.size);
    for (let ghost of ghosts.values()) {
        const newDirection = directions[Math.floor(Math.random()*4)];
        ghost.updateDirection(newDirection);
    }
    update();
    document.addEventListener("keyup", movePacman);
}

function loadImages() {
    wallImage = new Image();
    wallImage.src = "./wall.png";

    blueGhostImage = new Image();
    blueGhostImage.src = "./blueGhost.png";
    orangeGhostImage = new Image();
    orangeGhostImage.src = "./orangeGhost.png"
    pinkGhostImage = new Image()
    pinkGhostImage.src = "./pinkGhost.png";
    redGhostImage = new Image()
    redGhostImage.src = "./redGhost.png";
    scaredGhostImage = new Image();
    scaredGhostImage.src = "./scaredGhost.png";

    pacmanUpImage = new Image();
    pacmanUpImage.src = "./pacmanUp.png";
    pacmanDownImage = new Image();
    pacmanDownImage.src = "./pacmanDown.png";
    pacmanLeftImage = new Image();
    pacmanLeftImage.src = "./pacmanLeft.png";
    pacmanRightImage = new Image();
    pacmanRightImage.src = "./pacmanRight.png";
}

function loadMap() {
    walls.clear();
    foods.clear();
    powerPellets.clear();
    ghosts.clear();

    for (let r = 0; r < rowCount; r++) {
        for (let c = 0; c < columnCount; c++) {
            const row = tileMap[r];
            const tileMapChar = row[c];

            const x = c*tileSize;
            const y = r*tileSize;

            if (tileMapChar == 'X') { //block wall
                const wall = new Block(wallImage, x, y, tileSize, tileSize);
                walls.add(wall);  
            }
            else if (tileMapChar == 'b') { //blue ghost
                const ghost = new Block(blueGhostImage, x, y, tileSize, tileSize);
                ghost.originalImage = blueGhostImage;
                ghost.isScared = false;
                ghosts.add(ghost);
            }
            else if (tileMapChar == 'o') { //orange ghost
                const ghost = new Block(orangeGhostImage, x, y, tileSize, tileSize);
                ghost.originalImage = orangeGhostImage;
                ghost.isScared = false;
                ghosts.add(ghost);
            }
            else if (tileMapChar == 'p') { //pink ghost
                const ghost = new Block(pinkGhostImage, x, y, tileSize, tileSize);
                ghost.originalImage = pinkGhostImage;
                ghost.isScared = false;
                ghosts.add(ghost);
            }
            else if (tileMapChar == 'r') { //red ghost
                const ghost = new Block(redGhostImage, x, y, tileSize, tileSize);
                ghost.originalImage = redGhostImage;
                ghost.isScared = false;
                ghosts.add(ghost);
            }
            else if (tileMapChar == 'P') { //pacman
                pacman = new Block(pacmanRightImage, x, y, tileSize, tileSize);
            }
            else if (tileMapChar == ' ') { //empty is food
                const food = new Block(null, x + 14, y + 14, 4, 4);
                foods.add(food);
            }
            else if (tileMapChar == 'W') { //power pellet
                const powerPellet = new Block(null, x + 8, y + 8, 16, 16);
                powerPellets.add(powerPellet);
            }
        }
    }
}

function update() {
    if (gameOver) {
        return;
    }
    move();
    draw();
    setTimeout(update, 50); //1000/50 = 20 FPS
}

function draw() {
    context.clearRect(0, 0, board.width, board.height);

    // Draw pacman with fallback
    if (pacman.image && pacman.image.complete) {
        context.drawImage(pacman.image, pacman.x, pacman.y, pacman.width, pacman.height);
    } else {
        // Fallback: draw yellow circle for pacman
        context.fillStyle = "yellow";
        context.beginPath();
        context.arc(pacman.x + pacman.width/2, pacman.y + pacman.height/2, pacman.width/2, 0, 2 * Math.PI);
        context.fill();
    }

    // Draw ghosts with fallback
    for (let ghost of ghosts.values()) {
        if (ghost.image && ghost.image.complete) {
            context.drawImage(ghost.image, ghost.x, ghost.y, ghost.width, ghost.height);
        } else {
            // Fallback: draw colored rectangles for ghosts
            if (ghost.isScared) {
                context.fillStyle = "blue";
            } else {
                context.fillStyle = "red";
            }
            context.fillRect(ghost.x, ghost.y, ghost.width, ghost.height);
        }
    }

    // Draw walls with fallback
    for (let wall of walls.values()) {
        if (wall.image && wall.image.complete) {
            context.drawImage(wall.image, wall.x, wall.y, wall.width, wall.height);
        } else {
            // Fallback: draw blue rectangles for walls
            context.fillStyle = "blue";
            context.fillRect(wall.x, wall.y, wall.width, wall.height);
        }
    }

    // Draw food dots
    context.fillStyle = "white";
    for (let food of foods.values()) {
        context.fillRect(food.x, food.y, food.width, food.height);
    }

    //draw power pellets as larger white circles
    context.fillStyle = "white";
    for (let powerPellet of powerPellets.values()) {
        context.beginPath();
        context.arc(powerPellet.x + powerPellet.width/2, powerPellet.y + powerPellet.height/2, powerPellet.width/2, 0, 2 * Math.PI);
        context.fill();
    }

    //score
    context.fillStyle = "white";
    context.font="14px sans-serif";
    if (gameOver) {
        context.fillText("Game Over: " + String(score), tileSize/2, tileSize/2);
    }
    else {
        context.fillText("x" + String(lives) + " " + String(score), tileSize/2, tileSize/2);
    }
}

function move() {
    pacman.x += pacman.velocityX;
    pacman.y += pacman.velocityY;

    //check for screen wrapping (horizontal tunnels)
    if (pacman.x + pacman.width < 0) {
        //pacman went off left side, teleport to right side
        pacman.x = boardWidth;
    }
    else if (pacman.x > boardWidth) {
        //pacman went off right side, teleport to left side
        pacman.x = -pacman.width;
    }

    //check wall collisions
    for (let wall of walls.values()) {
        if (collision(pacman, wall)) {
            pacman.x -= pacman.velocityX;
            pacman.y -= pacman.velocityY;
            break;
        }
    }

    //update ghost scared state based on power mode
    for (let ghost of ghosts.values()) {
        if (powerMode && !ghost.isScared) {
            ghost.isScared = true;
            ghost.image = scaredGhostImage;
        } else if (!powerMode && ghost.isScared) {
            ghost.isScared = false;
            ghost.image = ghost.originalImage;
        }
    }

    //check ghosts collision
    let ghostEaten = null;
    for (let ghost of ghosts.values()) {
        if (collision(ghost, pacman)) {
            if (powerMode && ghost.isScared) {
                //eat the ghost
                ghostEaten = ghost;
                score += 200;
            } else {
                //pacman dies
                lives -= 1;
                if (lives == 0) {
                    gameOver = true;
                    return;
                }
                resetPositions();
                return;
            }
        }
    }

    //remove eaten ghost temporarily (respawn it)
    if (ghostEaten) {
        ghostEaten.reset();
        ghostEaten.isScared = false;
        ghostEaten.image = ghostEaten.originalImage;
    }

        if (ghost.y == tileSize*9 && ghost.direction != 'U' && ghost.direction != 'D') {
            ghost.updateDirection('U');
        }

        ghost.x += ghost.velocityX;
        ghost.y += ghost.velocityY;

        //check for screen wrapping for ghosts (horizontal tunnels)
        if (ghost.x + ghost.width < 0) {
            //ghost went off left side, teleport to right side
            ghost.x = boardWidth;
        }
        else if (ghost.x > boardWidth) {
            //ghost went off right side, teleport to left side
            ghost.x = -ghost.width;
        }

        for (let wall of walls.values()) {
            if (collision(ghost, wall)) {
                ghost.x -= ghost.velocityX;
                ghost.y -= ghost.velocityY;
                const newDirection = directions[Math.floor(Math.random()*4)];
                ghost.updateDirection(newDirection);
            }
        }
    }

    //check food collision
    let foodEaten = null;
    for (let food of foods.values()) {
        if (collision(pacman, food)) {
            foodEaten = food;
            score += 10;
            break;
        }
    }
    foods.delete(foodEaten);

    //check power pellet collision
    let powerPelletEaten = null;
    for (let powerPellet of powerPellets.values()) {
        if (collision(pacman, powerPellet)) {
            powerPelletEaten = powerPellet;
            score += 50;
            powerMode = true;
            powerModeTimer = powerModeDuration;
            break;
        }
    }
    powerPellets.delete(powerPelletEaten);

    //update power mode timer
    if (powerMode) {
        powerModeTimer--;
        if (powerModeTimer <= 0) {
            powerMode = false;
        }
    }

    //next level
    if (foods.size == 0 && powerPellets.size == 0) {
        loadMap();
        resetPositions();
    }
}

function movePacman(e) {
    if (gameOver) {
        loadMap();
        resetPositions();
        lives = 3;
        score = 0;
        gameOver = false;
        powerMode = false;
        powerModeTimer = 0;
        update(); //restart game loop
        return;
    }

    if (e.code == "ArrowUp" || e.code == "KeyW") {
        pacman.updateDirection('U');
    }
    else if (e.code == "ArrowDown" || e.code == "KeyS") {
        pacman.updateDirection('D');
    }
    else if (e.code == "ArrowLeft" || e.code == "KeyA") {
        pacman.updateDirection('L');
    }
    else if (e.code == "ArrowRight" || e.code == "KeyD") {
        pacman.updateDirection('R');
    }

    //update pacman images
    if (pacman.direction == 'U') {
        pacman.image = pacmanUpImage;
    }
    else if (pacman.direction == 'D') {
        pacman.image = pacmanDownImage;
    }
    else if (pacman.direction == 'L') {
        pacman.image = pacmanLeftImage;
    }
    else if (pacman.direction == 'R') {
        pacman.image = pacmanRightImage;
    }
    
}

function collision(a, b) {
    return a.x < b.x + b.width &&   //a's top left corner doesn't reach b's top right corner
           a.x + a.width > b.x &&   //a's top right corner passes b's top left corner
           a.y < b.y + b.height &&  //a's top left corner doesn't reach b's bottom left corner
           a.y + a.height > b.y;    //a's bottom left corner passes b's top left corner
}

function resetPositions() {
    pacman.reset();
    pacman.velocityX = 0;
    pacman.velocityY = 0;
    powerMode = false;
    powerModeTimer = 0;
    for (let ghost of ghosts.values()) {
        ghost.reset();
        ghost.isScared = false;
        ghost.image = ghost.originalImage;
        const newDirection = directions[Math.floor(Math.random()*4)];
        ghost.updateDirection(newDirection);
    }
}

class Block {
    constructor(image, x, y, width, height) {
        this.image = image;
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;

        this.startX = x;
        this.startY = y;

        this.direction = 'R';
        this.velocityX = 0;
        this.velocityY = 0;
    }

    updateDirection(direction) {
        const prevDirection = this.direction;
        this.direction = direction;
        this.updateVelocity();
        this.x += this.velocityX;
        this.y += this.velocityY;
        
        for (let wall of walls.values()) {
            if (collision(this, wall)) {
                this.x -= this.velocityX;
                this.y -= this.velocityY;
                this.direction = prevDirection;
                this.updateVelocity();
                return;
            }
        }
    }

    updateVelocity() {
        if (this.direction == 'U') {
            this.velocityX = 0;
            this.velocityY = -tileSize/4;
        }
        else if (this.direction == 'D') {
            this.velocityX = 0;
            this.velocityY = tileSize/4;
        }
        else if (this.direction == 'L') {
            this.velocityX = -tileSize/4;
            this.velocityY = 0;
        }
        else if (this.direction == 'R') {
            this.velocityX = tileSize/4;
            this.velocityY = 0;
        }
    }

    reset() {
        this.x = this.startX;
        this.y = this.startY;
    }
};
