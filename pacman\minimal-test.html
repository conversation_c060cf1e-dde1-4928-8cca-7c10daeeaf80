<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Pacman Test</title>
    <style>
        body {
            text-align: center;
            overflow: hidden;
        }
        #board {
            background-color: black;
        }
    </style>
</head>
<body>
    <canvas id="board"></canvas>
    
    <script>
        console.log("Script loaded");
        
        // Basic game variables
        const rowCount = 21;
        const columnCount = 19;
        const tileSize = 32;
        const boardWidth = columnCount * tileSize;
        const boardHeight = rowCount * tileSize;
        
        let board;
        let context;
        
        window.onload = function() {
            console.log("Window loaded");
            
            board = document.getElementById("board");
            board.height = boardHeight;
            board.width = boardWidth;
            context = board.getContext("2d");
            
            console.log("Canvas setup complete:", boardWidth, "x", boardHeight);
            
            // Draw a simple test pattern
            drawTest();
        };
        
        function drawTest() {
            console.log("Drawing test pattern");
            
            // Clear canvas
            context.clearRect(0, 0, board.width, board.height);
            
            // Draw walls (blue rectangles)
            context.fillStyle = "blue";
            for (let r = 0; r < rowCount; r++) {
                for (let c = 0; c < columnCount; c++) {
                    if (r === 0 || r === rowCount-1 || c === 0 || c === columnCount-1) {
                        context.fillRect(c * tileSize, r * tileSize, tileSize, tileSize);
                    }
                }
            }
            
            // Draw pacman (yellow circle)
            context.fillStyle = "yellow";
            context.beginPath();
            context.arc(9 * tileSize + tileSize/2, 15 * tileSize + tileSize/2, tileSize/2, 0, 2 * Math.PI);
            context.fill();
            
            // Draw some food dots
            context.fillStyle = "white";
            for (let r = 1; r < rowCount-1; r++) {
                for (let c = 1; c < columnCount-1; c++) {
                    if ((r + c) % 3 === 0) {
                        context.fillRect(c * tileSize + 14, r * tileSize + 14, 4, 4);
                    }
                }
            }
            
            // Draw power pellets
            context.fillStyle = "white";
            context.beginPath();
            context.arc(1 * tileSize + tileSize/2, 1 * tileSize + tileSize/2, 8, 0, 2 * Math.PI);
            context.fill();
            
            context.beginPath();
            context.arc((columnCount-2) * tileSize + tileSize/2, 1 * tileSize + tileSize/2, 8, 0, 2 * Math.PI);
            context.fill();
            
            console.log("Test pattern drawn successfully");
        }
    </script>
</body>
</html>
