<!DOCTYPE html>
<html>
<head>
    <title>Simple Canvas Test</title>
</head>
<body>
    <h1>Simple Canvas Test</h1>
    <canvas id="testCanvas" width="400" height="300" style="border: 1px solid black;"></canvas>
    
    <script>
        console.log("Script starting...");
        
        window.onload = function() {
            console.log("Window loaded");
            
            const canvas = document.getElementById("testCanvas");
            const ctx = canvas.getContext("2d");
            
            console.log("Canvas found:", canvas);
            console.log("Context:", ctx);
            
            // Draw a simple scene
            ctx.fillStyle = "black";
            ctx.fillRect(0, 0, 400, 300);
            
            ctx.fillStyle = "blue";
            ctx.fillRect(50, 50, 100, 100);
            
            ctx.fillStyle = "yellow";
            ctx.beginPath();
            ctx.arc(200, 150, 30, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = "white";
            ctx.fillRect(300, 100, 10, 10);
            
            console.log("Simple drawing complete");
        };
    </script>
</body>
</html>
